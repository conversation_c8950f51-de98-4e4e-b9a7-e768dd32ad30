/**
 * ComponentList组件的单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import ComponentList from '../ComponentList.vue'

// Mock组件
vi.mock('@/components/common/DataTable.vue', () => ({
  default: {
    name: 'DataTable',
    template: '<div data-testid="data-table"><slot name="actions"></slot><slot name="cell-status"></slot><slot name="actions" :record="{}"></slot></div>',
    props: ['data', 'columns', 'loading', 'title', 'searchable', 'search-placeholder'],
    emits: ['search', 'row-click']
  }
}))

vi.mock('@/components/common/StatusTag.vue', () => ({
  default: {
    name: 'StatusTag',
    template: '<span data-testid="status-tag">{{ status }}</span>',
    props: ['status']
  }
}))

vi.mock('@/components/common/Breadcrumb.vue', () => ({
  default: {
    name: 'Breadcrumb',
    template: '<nav data-testid="breadcrumb"></nav>',
    props: ['items']
  }
}))

vi.mock('@/components/common/FormField.vue', () => ({
  default: {
    name: 'FormField',
    template: '<div data-testid="form-field"><input :value="modelValue" @input="$emit(\'update:modelValue\', $event.target.value)" /></div>',
    props: ['modelValue', 'label', 'type', 'placeholder', 'required', 'error-message', 'options', 'rows'],
    emits: ['update:modelValue']
  }
}))

vi.mock('@/components/common/ConfirmDialog.vue', () => ({
  default: {
    name: 'ConfirmDialog',
    template: '<div data-testid="confirm-dialog" v-if="visible"></div>',
    props: ['visible', 'title', 'message', 'type', 'confirm-text', 'loading'],
    emits: ['confirm', 'cancel']
  }
}))

// Mock store
vi.mock('@/store/devops', () => ({
  useDevOpsStore: () => ({
    components: [],
    applications: [],
    loading: { components: false },
    loadComponents: vi.fn(),
    loadApplications: vi.fn(),
    createComponent: vi.fn(),
    updateComponent: vi.fn(),
    deleteComponent: vi.fn()
  })
}))

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/devops/components/:id', component: { template: '<div>Component Detail</div>' } },
    { path: '/devops/applications/:id', component: { template: '<div>Application Detail</div>' } },
    { path: '/devops/resources', component: { template: '<div>Resources</div>' } }
  ]
})

describe('ComponentList', () => {
  let wrapper: any
  let pinia: any

  beforeEach(() => {
    pinia = createPinia()
    wrapper = mount(ComponentList, {
      global: {
        plugins: [router, pinia]
      }
    })
  })

  it('renders correctly', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('[data-testid="breadcrumb"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="data-table"]').exists()).toBe(true)
  })

  it('displays page header with title and description', () => {
    const header = wrapper.find('.page-header')
    expect(header.exists()).toBe(true)
    expect(header.text()).toContain('组件管理')
    expect(header.text()).toContain('管理应用的各个组件，配置代码仓库和构建部署流程')
  })

  it('shows create component button', () => {
    const createButton = wrapper.find('.btn-primary')
    expect(createButton.exists()).toBe(true)
    expect(createButton.text()).toContain('创建组件')
  })

  it('displays filter section with application, repository type and status filters', () => {
    const filtersSection = wrapper.find('.filters-section')
    expect(filtersSection.exists()).toBe(true)
    
    const filterSelects = wrapper.findAll('.filter-select')
    expect(filterSelects).toHaveLength(3)
  })

  it('opens create modal when create button is clicked', async () => {
    const createButton = wrapper.find('.btn-primary')
    await createButton.trigger('click')
    
    expect(wrapper.vm.showCreateModal).toBe(true)
    expect(wrapper.find('.modal-overlay').exists()).toBe(true)
  })

  it('displays modal with correct title when creating', async () => {
    wrapper.vm.showCreateModal = true
    await wrapper.vm.$nextTick()
    
    const modalHeader = wrapper.find('.modal-header h3')
    expect(modalHeader.text()).toBe('创建组件')
  })

  it('displays modal with correct title when editing', async () => {
    wrapper.vm.showEditModal = true
    await wrapper.vm.$nextTick()
    
    const modalHeader = wrapper.find('.modal-header h3')
    expect(modalHeader.text()).toBe('编辑组件')
  })

  it('validates form before submission', async () => {
    wrapper.vm.showCreateModal = true
    await wrapper.vm.$nextTick()
    
    // 尝试提交空表单
    const form = wrapper.find('.modal-form')
    await form.trigger('submit')
    
    expect(wrapper.vm.formErrors.name).toBeTruthy()
    expect(wrapper.vm.formErrors.applicationId).toBeTruthy()
  })

  it('validates repository URL format', () => {
    wrapper.vm.formData.repositoryUrl = 'invalid-url'
    wrapper.vm.formData.repositoryType = 'GIT'
    
    const isValid = wrapper.vm.validateForm()
    expect(isValid).toBe(true) // URL validation is done separately in validateRepository
  })

  it('filters components by application', async () => {
    // 设置测试数据
    wrapper.vm.components = [
      { id: 1, name: '组件1', applicationId: 1, status: 'ACTIVE' },
      { id: 2, name: '组件2', applicationId: 2, status: 'ACTIVE' }
    ]
    
    wrapper.vm.selectedApplicationId = 1
    await wrapper.vm.$nextTick()
    
    const filtered = wrapper.vm.filteredComponents
    expect(filtered).toHaveLength(1)
    expect(filtered[0].applicationId).toBe(1)
  })

  it('filters components by repository type', async () => {
    // 设置测试数据
    wrapper.vm.components = [
      { id: 1, name: '组件1', applicationId: 1, repositoryType: 'GIT', status: 'ACTIVE' },
      { id: 2, name: '组件2', applicationId: 1, repositoryType: 'SVN', status: 'ACTIVE' }
    ]
    
    wrapper.vm.selectedRepositoryType = 'GIT'
    await wrapper.vm.$nextTick()
    
    const filtered = wrapper.vm.filteredComponents
    expect(filtered).toHaveLength(1)
    expect(filtered[0].repositoryType).toBe('GIT')
  })

  it('filters components by status', async () => {
    // 设置测试数据
    wrapper.vm.components = [
      { id: 1, name: '组件1', applicationId: 1, status: 'ACTIVE' },
      { id: 2, name: '组件2', applicationId: 1, status: 'BUILDING' }
    ]
    
    wrapper.vm.selectedStatus = 'ACTIVE'
    await wrapper.vm.$nextTick()
    
    const filtered = wrapper.vm.filteredComponents
    expect(filtered).toHaveLength(1)
    expect(filtered[0].status).toBe('ACTIVE')
  })

  it('formats repository URL correctly', () => {
    const url = 'https://github.com/company/repo.git'
    const formatted = wrapper.vm.formatRepositoryUrl(url)
    
    expect(formatted).toBe('repo')
  })

  it('handles invalid repository URL gracefully', () => {
    const invalidUrl = 'not-a-url'
    const formatted = wrapper.vm.formatRepositoryUrl(invalidUrl)
    
    expect(formatted).toBe(invalidUrl)
  })

  it('gets application name correctly', () => {
    wrapper.vm.applications = [
      { id: 1, name: '测试应用', projectId: 1, status: 'ACTIVE' }
    ]
    
    const applicationName = wrapper.vm.getApplicationName(1)
    expect(applicationName).toBe('测试应用')
    
    const unknownApplicationName = wrapper.vm.getApplicationName(999)
    expect(unknownApplicationName).toBe('未知应用')
  })

  it('opens repository in new tab', () => {
    const openSpy = vi.spyOn(window, 'open').mockImplementation(() => null)
    const repositoryUrl = 'https://github.com/company/repo.git'
    
    wrapper.vm.openRepository(repositoryUrl)
    
    expect(openSpy).toHaveBeenCalledWith(repositoryUrl, '_blank')
    openSpy.mockRestore()
  })

  it('handles navigation to component detail', () => {
    const pushSpy = vi.spyOn(router, 'push')
    const testComponent = { id: 1, name: '测试组件', applicationId: 1, status: 'ACTIVE' }
    
    wrapper.vm.viewComponent(testComponent)
    
    expect(pushSpy).toHaveBeenCalledWith('/devops/components/1')
  })

  it('handles navigation to resources', () => {
    const pushSpy = vi.spyOn(router, 'push')
    const testComponent = { id: 1, name: '测试组件', applicationId: 1, status: 'ACTIVE' }
    
    wrapper.vm.viewResources(testComponent)
    
    expect(pushSpy).toHaveBeenCalledWith('/devops/resources?componentId=1')
  })

  it('handles navigation to application detail', () => {
    const pushSpy = vi.spyOn(router, 'push')
    
    wrapper.vm.viewApplication(1)
    
    expect(pushSpy).toHaveBeenCalledWith('/devops/applications/1')
  })

  it('shows delete confirmation dialog', async () => {
    const testComponent = { id: 1, name: '测试组件', applicationId: 1, status: 'ACTIVE' }
    wrapper.vm.deleteComponent(testComponent)
    
    expect(wrapper.vm.showDeleteDialog).toBe(true)
    expect(wrapper.vm.deleteTarget).toEqual(testComponent)
  })

  it('resets form when closing modal', async () => {
    // 设置表单数据
    wrapper.vm.formData.name = '测试组件'
    wrapper.vm.formData.description = '测试描述'
    wrapper.vm.formErrors.name = '错误信息'
    
    wrapper.vm.closeModal()
    
    expect(wrapper.vm.formData.name).toBe('')
    expect(wrapper.vm.formData.description).toBe('')
    expect(wrapper.vm.formErrors.name).toBe('')
  })

  it('computes application options correctly', () => {
    wrapper.vm.applications = [
      { id: 1, name: '应用1', projectId: 1, status: 'ACTIVE' },
      { id: 2, name: '应用2', projectId: 1, status: 'ACTIVE' }
    ]
    
    const options = wrapper.vm.applicationOptions
    expect(options).toHaveLength(2)
    expect(options[0]).toEqual({ label: '应用1', value: 1 })
    expect(options[1]).toEqual({ label: '应用2', value: 2 })
  })

  it('handles search functionality', async () => {
    const searchQuery = '用户服务'
    wrapper.vm.handleSearch(searchQuery)
    
    expect(wrapper.vm.searchQuery).toBe(searchQuery)
  })

  it('formats date correctly', () => {
    const dateString = '2024-01-15T10:30:00Z'
    const formatted = wrapper.vm.formatDate(dateString)
    
    expect(formatted).toContain('2024')
    expect(typeof formatted).toBe('string')
  })
})
