<template>
  <div class="component-list">
    <!-- 面包屑导航 -->
    <Breadcrumb :items="breadcrumbItems" />

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h1>组件管理</h1>
          <p>管理应用的各个组件，配置代码仓库和构建部署流程</p>
        </div>
        <div class="header-actions">
          <button
            @click="showCreateModal = true"
            class="btn btn-primary"
          >
            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            创建组件
          </button>
        </div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters-section">
      <div class="filter-group">
        <label class="filter-label">按应用筛选：</label>
        <select
          v-model="selectedApplicationId"
          @change="handleApplicationFilter"
          class="filter-select"
        >
          <option value="">全部应用</option>
          <option
            v-for="application in applications"
            :key="application.id"
            :value="application.id"
          >
            {{ application.name }}
          </option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">仓库类型：</label>
        <select
          v-model="selectedRepositoryType"
          @change="handleRepositoryTypeFilter"
          class="filter-select"
        >
          <option value="">全部类型</option>
          <option value="GIT">Git</option>
          <option value="SVN">SVN</option>
          <option value="MERCURIAL">Mercurial</option>
        </select>
      </div>
      <div class="filter-group">
        <label class="filter-label">状态筛选：</label>
        <select
          v-model="selectedStatus"
          @change="handleStatusFilter"
          class="filter-select"
        >
          <option value="">全部状态</option>
          <option value="ACTIVE">活跃</option>
          <option value="INACTIVE">非活跃</option>
          <option value="BUILDING">构建中</option>
        </select>
      </div>
    </div>

    <!-- 数据表格 -->
    <DataTable
      :data="filteredComponents"
      :columns="tableColumns"
      :loading="loading"
      title="组件列表"
      searchable
      search-placeholder="搜索组件名称或描述..."
      @search="handleSearch"
      @row-click="handleRowClick"
    >
      <template #toolbar-actions>
        <button
          @click="refreshData"
          :disabled="loading"
          class="btn btn-secondary"
        >
          <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          刷新
        </button>
      </template>

      <template #cell-applicationName="{ record }">
        <span class="application-link" @click="viewApplication(record.applicationId)">
          {{ getApplicationName(record.applicationId) }}
        </span>
      </template>

      <template #cell-repositoryUrl="{ value, record }">
        <div class="repository-info">
          <a
            v-if="value"
            :href="value"
            target="_blank"
            class="repository-link"
            @click.stop
          >
            <svg class="repository-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
            {{ formatRepositoryUrl(value) }}
          </a>
          <span v-else class="no-repository">未配置</span>
          <div v-if="record.repositoryBranch" class="repository-branch">
            <svg class="branch-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm2 10a1 1 0 10-2 0v3a1 1 0 102 0v-3zm2-3a1 1 0 011 1v5a1 1 0 11-2 0v-5a1 1 0 011-1zm4-1a1 1 0 10-2 0v7a1 1 0 102 0V8z" clip-rule="evenodd" />
            </svg>
            {{ record.repositoryBranch }}
          </div>
        </div>
      </template>

      <template #cell-repositoryType="{ value }">
        <span class="repository-type" :class="`type-${value?.toLowerCase()}`">
          {{ value || '-' }}
        </span>
      </template>

      <template #cell-status="{ value }">
        <StatusTag :status="value" />
      </template>

      <template #cell-createdAt="{ value }">
        {{ formatDate(value) }}
      </template>

      <template #actions="{ record }">
        <div class="action-buttons">
          <button
            @click="viewComponent(record)"
            class="action-btn action-btn-view"
            title="查看详情"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
              <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            @click="viewResources(record)"
            class="action-btn action-btn-resources"
            title="查看资源"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M3 7v10a2 2 0 002 2h14l-2-2H5V7H3zM14 2H8a2 2 0 00-2 2v8a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2zM8 4h6v8H8V4z" />
            </svg>
          </button>
          <button
            v-if="record.repositoryUrl"
            @click="openRepository(record.repositoryUrl)"
            class="action-btn action-btn-repo"
            title="打开仓库"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
              <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
            </svg>
          </button>
          <button
            @click="editComponent(record)"
            class="action-btn action-btn-edit"
            title="编辑组件"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
          </button>
          <button
            @click="deleteComponent(record)"
            class="action-btn action-btn-delete"
            title="删除组件"
          >
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </template>
    </DataTable>

    <!-- 创建/编辑组件模态框 -->
    <div v-if="showCreateModal || showEditModal" class="modal-overlay" @click="closeModal">
      <div class="modal-container modal-large" @click.stop>
        <div class="modal-header">
          <h3>{{ showCreateModal ? '创建组件' : '编辑组件' }}</h3>
          <button @click="closeModal" class="modal-close">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="submitForm" class="modal-form">
          <div class="form-grid">
            <div class="form-column">
              <FormField
                v-model="formData.name"
                label="组件名称"
                type="text"
                placeholder="请输入组件名称"
                required
                :error-message="formErrors.name"
              />

              <FormField
                v-model="formData.description"
                label="组件描述"
                type="textarea"
                placeholder="请输入组件描述"
                :rows="3"
                :error-message="formErrors.description"
              />

              <FormField
                v-model="formData.applicationId"
                label="所属应用"
                type="select"
                :options="applicationOptions"
                required
                :error-message="formErrors.applicationId"
              />

              <FormField
                v-model="formData.status"
                label="组件状态"
                type="select"
                :options="statusOptions"
                :error-message="formErrors.status"
              />
            </div>

            <div class="form-column">
              <FormField
                v-model="formData.repositoryUrl"
                label="代码仓库URL"
                type="url"
                placeholder="https://github.com/user/repo.git"
                :error-message="formErrors.repositoryUrl"
              />

              <FormField
                v-model="formData.repositoryType"
                label="仓库类型"
                type="select"
                :options="repositoryTypeOptions"
                :error-message="formErrors.repositoryType"
              />

              <FormField
                v-model="formData.repositoryBranch"
                label="默认分支"
                type="text"
                placeholder="main"
                :error-message="formErrors.repositoryBranch"
              />

              <FormField
                v-model="formData.buildPath"
                label="构建路径"
                type="text"
                placeholder="/"
                :error-message="formErrors.buildPath"
              />
            </div>
          </div>

          <div class="modal-actions">
            <button type="button" @click="closeModal" class="btn btn-secondary">
              取消
            </button>
            <button
              v-if="formData.repositoryUrl"
              type="button"
              @click="validateRepository"
              :disabled="validatingRepo"
              class="btn btn-outline"
            >
              <svg v-if="validatingRepo" class="btn-icon animate-spin" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 3a7 7 0 100 14 7 7 0 000-14zM2 10a8 8 0 1116 0 8 8 0 01-16 0z" opacity="0.3"/>
                <path d="M10 2a8 8 0 018 8" fill="currentColor">
                  <animateTransform attributeName="transform" type="rotate" from="0 10 10" to="360 10 10" dur="1s" repeatCount="indefinite"/>
                </path>
              </svg>
              {{ validatingRepo ? '验证中...' : '验证仓库' }}
            </button>
            <button type="submit" :disabled="submitting" class="btn btn-primary">
              <svg v-if="submitting" class="btn-icon animate-spin" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 3a7 7 0 100 14 7 7 0 000-14zM2 10a8 8 0 1116 0 8 8 0 01-16 0z" opacity="0.3"/>
                <path d="M10 2a8 8 0 018 8" fill="currentColor">
                  <animateTransform attributeName="transform" type="rotate" from="0 10 10" to="360 10 10" dur="1s" repeatCount="indefinite"/>
                </path>
              </svg>
              {{ submitting ? '保存中...' : '保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 确认删除对话框 -->
    <ConfirmDialog
      :visible="showDeleteDialog"
      title="确认删除组件"
      :message="`确定要删除组件 &quot;${deleteTarget?.name}&quot; 吗？此操作将同时删除该组件下的所有资源，且不可撤销。`"
      type="danger"
      confirm-text="删除"
      :loading="deleting"
      @confirm="confirmDelete"
      @cancel="showDeleteDialog = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import DataTable from '@/components/common/DataTable.vue'
import StatusTag from '@/components/common/StatusTag.vue'
import Breadcrumb from '@/components/common/Breadcrumb.vue'
import FormField from '@/components/common/FormField.vue'
import ConfirmDialog from '@/components/common/ConfirmDialog.vue'
import { useDevOpsStore } from '@/store/devops'
import type { DevOpsComponent, DevOpsApplication, BreadcrumbItem, TableColumn } from '@/types/devops'

const router = useRouter()
const route = useRoute()
const devopsStore = useDevOpsStore()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const deleting = ref(false)
const validatingRepo = ref(false)
const components = ref<DevOpsComponent[]>([])
const applications = ref<DevOpsApplication[]>([])
const searchQuery = ref('')
const selectedApplicationId = ref<number | ''>('')
const selectedRepositoryType = ref('')
const selectedStatus = ref('')

// 模态框状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteDialog = ref(false)
const deleteTarget = ref<DevOpsComponent | null>(null)

// 表单数据
const formData = ref({
  name: '',
  description: '',
  applicationId: null as number | null,
  repositoryUrl: '',
  repositoryType: 'GIT',
  repositoryBranch: 'main',
  buildPath: '/',
  status: 'ACTIVE'
})

const formErrors = ref({
  name: '',
  description: '',
  applicationId: '',
  repositoryUrl: '',
  repositoryType: '',
  repositoryBranch: '',
  buildPath: '',
  status: ''
})

const editingComponent = ref<DevOpsComponent | null>(null)

// 计算属性
const breadcrumbItems = computed<BreadcrumbItem[]>(() => [
  { title: 'DevOps 管理', path: '/devops', icon: 'home' },
  { title: '组件管理', active: true, icon: 'component' }
])

const tableColumns: TableColumn[] = [
  { key: 'name', title: '组件名称', sortable: true },
  { key: 'applicationName', title: '所属应用', width: '150px' },
  { key: 'repositoryUrl', title: '代码仓库', width: '200px' },
  { key: 'repositoryType', title: '仓库类型', width: '100px' },
  { key: 'status', title: '状态', width: '120px' },
  { key: 'createdAt', title: '创建时间', width: '180px', sortable: true }
]

const applicationOptions = computed(() =>
  applications.value.map(app => ({
    label: app.name,
    value: app.id!
  }))
)

const statusOptions = [
  { label: '活跃', value: 'ACTIVE' },
  { label: '非活跃', value: 'INACTIVE' },
  { label: '构建中', value: 'BUILDING' }
]

const repositoryTypeOptions = [
  { label: 'Git', value: 'GIT' },
  { label: 'SVN', value: 'SVN' },
  { label: 'Mercurial', value: 'MERCURIAL' }
]

const filteredComponents = computed(() => {
  let result = [...components.value]

  // 按应用筛选
  if (selectedApplicationId.value) {
    result = result.filter(comp => comp.applicationId === selectedApplicationId.value)
  }

  // 按仓库类型筛选
  if (selectedRepositoryType.value) {
    result = result.filter(comp => comp.repositoryType === selectedRepositoryType.value)
  }

  // 按状态筛选
  if (selectedStatus.value) {
    result = result.filter(comp => comp.status === selectedStatus.value)
  }

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(comp =>
      comp.name.toLowerCase().includes(query) ||
      (comp.description && comp.description.toLowerCase().includes(query))
    )
  }

  return result
})

// 方法
const loadComponents = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟数据
    components.value = [
      {
        id: 1,
        name: '用户认证服务',
        description: '处理用户登录、注册和权限验证',
        applicationId: 1,
        repositoryUrl: 'https://github.com/company/user-auth-service.git',
        repositoryType: 'GIT',
        repositoryBranch: 'main',
        buildPath: '/',
        status: 'ACTIVE',
        createdAt: '2024-01-15T10:30:00Z',
        userId: 1
      },
      {
        id: 2,
        name: '订单处理引擎',
        description: '订单创建、更新和状态管理',
        applicationId: 1,
        repositoryUrl: 'https://github.com/company/order-engine.git',
        repositoryType: 'GIT',
        repositoryBranch: 'develop',
        buildPath: '/src',
        status: 'ACTIVE',
        createdAt: '2024-01-16T14:20:00Z',
        userId: 1
      },
      {
        id: 3,
        name: '支付网关',
        description: '第三方支付接口集成和处理',
        applicationId: 1,
        repositoryUrl: 'https://github.com/company/payment-gateway.git',
        repositoryType: 'GIT',
        repositoryBranch: 'main',
        buildPath: '/',
        status: 'BUILDING',
        createdAt: '2024-01-17T09:15:00Z',
        userId: 1
      },
      {
        id: 4,
        name: '移动端API网关',
        description: '移动应用API统一入口',
        applicationId: 2,
        repositoryUrl: 'https://github.com/company/mobile-api-gateway.git',
        repositoryType: 'GIT',
        repositoryBranch: 'main',
        buildPath: '/',
        status: 'ACTIVE',
        createdAt: '2024-01-20T16:45:00Z',
        userId: 1
      }
    ]
  } finally {
    loading.value = false
  }
}

const loadApplications = async () => {
  try {
    // 模拟应用数据
    applications.value = [
      { id: 1, name: '用户服务', description: '用户管理服务', projectId: 1, status: 'ACTIVE', createdAt: '2024-01-15T10:30:00Z', userId: 1 },
      { id: 2, name: '订单服务', description: '订单处理服务', projectId: 1, status: 'ACTIVE', createdAt: '2024-01-16T14:20:00Z', userId: 1 },
      { id: 3, name: '移动端API', description: '移动应用API', projectId: 2, status: 'ACTIVE', createdAt: '2024-01-20T16:45:00Z', userId: 1 }
    ]
  } catch (error) {
    console.error('加载应用失败:', error)
  }
}

const refreshData = () => {
  loadComponents()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
}

const handleApplicationFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleRepositoryTypeFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleStatusFilter = () => {
  // 筛选逻辑在计算属性中处理
}

const handleRowClick = (component: DevOpsComponent) => {
  viewComponent(component)
}

const viewApplication = (applicationId: number) => {
  router.push(`/devops/applications/${applicationId}`)
}

const viewComponent = (component: DevOpsComponent) => {
  router.push(`/devops/components/${component.id}`)
}

const viewResources = (component: DevOpsComponent) => {
  router.push(`/devops/resources?componentId=${component.id}`)
}

const openRepository = (repositoryUrl: string) => {
  window.open(repositoryUrl, '_blank')
}

const editComponent = (component: DevOpsComponent) => {
  editingComponent.value = component
  formData.value = {
    name: component.name,
    description: component.description || '',
    applicationId: component.applicationId,
    repositoryUrl: component.repositoryUrl || '',
    repositoryType: component.repositoryType || 'GIT',
    repositoryBranch: component.repositoryBranch || 'main',
    buildPath: component.buildPath || '/',
    status: component.status || 'ACTIVE'
  }
  showEditModal.value = true
}

const deleteComponent = (component: DevOpsComponent) => {
  deleteTarget.value = component
  showDeleteDialog.value = true
}

const getApplicationName = (applicationId: number) => {
  const application = applications.value.find(a => a.id === applicationId)
  return application?.name || '未知应用'
}

const formatRepositoryUrl = (url: string) => {
  try {
    const urlObj = new URL(url)
    const pathParts = urlObj.pathname.split('/')
    return pathParts[pathParts.length - 1].replace('.git', '')
  } catch {
    return url
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const validateRepository = async () => {
  if (!formData.value.repositoryUrl) return

  validatingRepo.value = true
  try {
    // 模拟仓库验证
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 简单的URL格式验证
    const urlPattern = /^https?:\/\/.+\.(git|svn)$/
    if (!urlPattern.test(formData.value.repositoryUrl)) {
      formErrors.value.repositoryUrl = '仓库URL格式不正确'
      return
    }

    formErrors.value.repositoryUrl = ''
    alert('仓库验证成功！')
  } catch (error) {
    formErrors.value.repositoryUrl = '仓库验证失败，请检查URL是否正确'
  } finally {
    validatingRepo.value = false
  }
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  resetForm()
}

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    applicationId: null,
    repositoryUrl: '',
    repositoryType: 'GIT',
    repositoryBranch: 'main',
    buildPath: '/',
    status: 'ACTIVE'
  }
  formErrors.value = {
    name: '',
    description: '',
    applicationId: '',
    repositoryUrl: '',
    repositoryType: '',
    repositoryBranch: '',
    buildPath: '',
    status: ''
  }
  editingComponent.value = null
}

const validateForm = () => {
  const errors = {
    name: '',
    description: '',
    applicationId: '',
    repositoryUrl: '',
    repositoryType: '',
    repositoryBranch: '',
    buildPath: '',
    status: ''
  }
  let isValid = true

  if (!formData.value.name.trim()) {
    errors.name = '组件名称不能为空'
    isValid = false
  }

  if (!formData.value.applicationId) {
    errors.applicationId = '请选择所属应用'
    isValid = false
  }

  if (formData.value.repositoryUrl && !formData.value.repositoryType) {
    errors.repositoryType = '请选择仓库类型'
    isValid = false
  }

  formErrors.value = errors
  return isValid
}

const submitForm = async () => {
  if (!validateForm()) return

  submitting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (showCreateModal.value) {
      // 创建组件
      const newComponent: DevOpsComponent = {
        id: Date.now(),
        ...formData.value,
        applicationId: formData.value.applicationId!,
        createdAt: new Date().toISOString(),
        userId: 1
      }
      components.value.unshift(newComponent)
    } else if (showEditModal.value && editingComponent.value) {
      // 更新组件
      const index = components.value.findIndex(c => c.id === editingComponent.value!.id)
      if (index !== -1) {
        components.value[index] = {
          ...components.value[index],
          ...formData.value,
          applicationId: formData.value.applicationId!,
          updatedAt: new Date().toISOString()
        }
      }
    }

    closeModal()
  } finally {
    submitting.value = false
  }
}

const confirmDelete = async () => {
  if (!deleteTarget.value) return

  deleting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    const index = components.value.findIndex(c => c.id === deleteTarget.value!.id)
    if (index !== -1) {
      components.value.splice(index, 1)
    }

    showDeleteDialog.value = false
    deleteTarget.value = null
  } finally {
    deleting.value = false
  }
}

// 生命周期
onMounted(() => {
  loadApplications()
  loadComponents()

  // 如果URL中有applicationId参数，自动筛选
  const applicationId = route.query.applicationId
  if (applicationId) {
    selectedApplicationId.value = Number(applicationId)
  }
})
</script>

<style scoped>
.component-list {
  @apply p-6 bg-gray-50 min-h-screen;
}

/* 页面头部 */
.page-header {
  @apply mb-6;
}

.header-content {
  @apply flex items-center justify-between;
}

.header-title h1 {
  @apply text-2xl font-bold text-gray-900 m-0;
}

.header-title p {
  @apply text-gray-600 mt-1 m-0;
}

.header-actions {
  @apply flex items-center gap-3;
}

/* 筛选器部分 */
.filters-section {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6 flex items-center gap-6;
}

.filter-group {
  @apply flex items-center gap-2;
}

.filter-label {
  @apply text-sm font-medium text-gray-700 whitespace-nowrap;
}

.filter-select {
  @apply px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none;
  min-width: 150px;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500;
}

.btn-outline {
  @apply bg-transparent text-blue-600 border border-blue-300 hover:bg-blue-50 focus:ring-blue-500;
}

.btn-icon {
  @apply w-4 h-4;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 应用链接 */
.application-link {
  @apply text-blue-600 hover:text-blue-800 cursor-pointer hover:underline transition-colors;
}

/* 仓库信息 */
.repository-info {
  @apply flex flex-col gap-1;
}

.repository-link {
  @apply flex items-center gap-1 text-blue-600 hover:text-blue-800 hover:underline transition-colors;
  text-decoration: none;
}

.repository-icon {
  @apply w-4 h-4;
}

.no-repository {
  @apply text-gray-400 text-sm;
}

.repository-branch {
  @apply flex items-center gap-1 text-xs text-gray-500;
}

.branch-icon {
  @apply w-3 h-3;
}

/* 仓库类型 */
.repository-type {
  @apply px-2 py-1 text-xs font-medium rounded-full;
}

.repository-type.type-git {
  @apply bg-orange-100 text-orange-800;
}

.repository-type.type-svn {
  @apply bg-blue-100 text-blue-800;
}

.repository-type.type-mercurial {
  @apply bg-green-100 text-green-800;
}

/* 操作按钮 */
.action-buttons {
  @apply flex items-center gap-1;
}

.action-btn {
  @apply p-2 rounded-md transition-colors;
}

.action-btn svg {
  @apply w-4 h-4;
}

.action-btn-view {
  @apply text-blue-600 hover:bg-blue-50;
}

.action-btn-resources {
  @apply text-purple-600 hover:bg-purple-50;
}

.action-btn-repo {
  @apply text-gray-600 hover:bg-gray-50;
}

.action-btn-edit {
  @apply text-green-600 hover:bg-green-50;
}

.action-btn-delete {
  @apply text-red-600 hover:bg-red-50;
}

/* 模态框样式 */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl w-full max-w-md;
}

.modal-large {
  @apply max-w-4xl;
}

.modal-header {
  @apply flex items-center justify-between p-6 pb-4;
}

.modal-header h3 {
  @apply text-lg font-semibold text-gray-900 m-0;
}

.modal-close {
  @apply text-gray-400 hover:text-gray-600 p-1 rounded;
}

.modal-close svg {
  @apply w-5 h-5;
}

.modal-form {
  @apply px-6 pb-6;
}

.form-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.form-column {
  @apply space-y-4;
}

.modal-actions {
  @apply flex items-center justify-end gap-3 mt-6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .component-list {
    @apply p-4;
  }

  .header-content {
    @apply flex-col items-start gap-4;
  }

  .filters-section {
    @apply flex-col items-start gap-4;
  }

  .filter-group {
    @apply w-full;
  }

  .filter-select {
    @apply w-full;
  }

  .modal-container {
    @apply max-w-full mx-4;
  }

  .form-grid {
    @apply grid-cols-1;
  }

  .action-buttons {
    @apply flex-wrap gap-2;
  }

  .repository-info {
    @apply text-xs;
  }
}

/* 动画效果 */
.modal-overlay {
  animation: fadeIn 0.2s ease-out;
}

.modal-container {
  animation: slideIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 悬停效果 */
.action-btn:hover {
  @apply transform scale-105;
}

.application-link:hover,
.repository-link:hover {
  @apply transform translateX-1;
}

/* 表单验证状态 */
.form-field.error input,
.form-field.error textarea,
.form-field.error select {
  @apply border-red-300 focus:ring-red-500 focus:border-red-500;
}
</style>
